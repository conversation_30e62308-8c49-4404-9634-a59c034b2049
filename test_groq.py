#!/usr/bin/env python3
"""
Test Groq API connectivity
"""

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_groq_api():
    """Test Groq API with a simple request"""
    
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key or api_key == "your_groq_api_key_here":
        print("❌ GROQ_API_KEY not found or not set in .env file")
        print("Please get your API key from https://console.groq.com/")
        return False
    
    print(f"🔑 Using API key: {api_key[:10]}...{api_key[-4:]}")
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "messages": [
            {
                "role": "user",
                "content": "Hello, can you respond with just 'API working' to test the connection?"
            }
        ],
        "model": "llama-3.3-70b-versatile",
        "stream": False,
        "temperature": 0.1,
        "max_tokens": 10
    }
    
    try:
        response = requests.post(
            "https://api.groq.com/openai/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"✅ Response: {content}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_medical_query():
    """Test with a medical query"""
    
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key or api_key == "your_groq_api_key_here":
        print("❌ GROQ_API_KEY not set")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "messages": [
            {
                "role": "system",
                "content": "You are a veterinary medical assistant."
            },
            {
                "role": "user",
                "content": "What is acidosis in cattle? Give a brief 2-sentence answer."
            }
        ],
        "model": "llama-3.3-70b-versatile",
        "stream": False,
        "temperature": 0.3,
        "max_tokens": 100
    }
    
    try:
        response = requests.post(
            "https://api.groq.com/openai/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"🧠 Medical response: {content}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Groq API Connection")
    print("=" * 40)
    
    # Test basic API functionality
    print("\n1. Testing basic API connection...")
    basic_test = test_groq_api()
    
    if basic_test:
        print("\n2. Testing medical query...")
        medical_test = test_medical_query()
        
        if medical_test:
            print("\n✅ All tests passed! Groq API is working correctly!")
            print("You can now use the medical search system with Groq LLM.")
        else:
            print("\n⚠️  Basic connection works but medical query failed.")
    else:
        print("\n❌ Basic API test failed. Please check:")
        print("   1. API key is correct in .env file")
        print("   2. Internet connection")
        print("   3. Groq API service status")
        print("   4. API credits/limits")
        
    print("\n💡 To get a Groq API key:")
    print("   1. Visit https://console.groq.com/")
    print("   2. Sign up for a free account")
    print("   3. Generate an API key")
    print("   4. Add it to your .env file as GROQ_API_KEY=your_key_here")
