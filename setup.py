#!/usr/bin/env python3
"""
Setup script for Medical Document Search System
"""

import subprocess
import sys
import os
import requests
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_ollama():
    """Check if Ollama is running and has the required model"""
    print("🔍 Checking Ollama service...")
    
    try:
        # Check if Ollama is running
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama service is running")
            
            # Check for nomic-embed-text-v1 model
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]
            
            if any("nomic-embed-text" in name for name in model_names):
                print("✅ Nomic embedding model found")
                return True
            else:
                print("⚠️  Nomic embedding model not found")
                print("Please install it with: ollama pull nomic-embed-text-v1")
                return False
        else:
            print("❌ Ollama service not responding")
            return False
            
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to Ollama service")
        print("Please make sure Ollama is installed and running on localhost:11434")
        return False

def check_postgresql():
    """Check PostgreSQL connection"""
    print("🔍 Checking PostgreSQL connection...")
    
    try:
        import psycopg2
        from dotenv import load_dotenv
        
        load_dotenv()
        
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"), 
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        # Check for pgvector extension
        with conn.cursor() as cur:
            cur.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector'")
            if cur.fetchone():
                print("✅ PostgreSQL connected with pgvector extension")
            else:
                print("⚠️  pgvector extension not found")
                print("Please install pgvector extension in your PostgreSQL database")
                
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print("Please check your database configuration in .env file")
        return False

def check_grok_api():
    """Check Grok API key"""
    print("🔍 Checking Grok API configuration...")
    
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("GROK_API_KEY")
    if not api_key:
        print("❌ GROK_API_KEY not found in .env file")
        return False
        
    if api_key.startswith("gsk_"):
        print("✅ Grok API key found")
        return True
    else:
        print("⚠️  Grok API key format may be incorrect")
        return True

def check_diseases_folder():
    """Check if diseases folder exists and has PDFs"""
    print("🔍 Checking diseases folder...")
    
    diseases_path = Path("diseases")
    if not diseases_path.exists():
        print("❌ diseases folder not found")
        return False
        
    pdf_files = list(diseases_path.glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found in diseases folder")
        return False
        
    print(f"✅ Found {len(pdf_files)} PDF files in diseases folder")
    return True

def create_env_file():
    """Create .env file if it doesn't exist"""
    env_path = Path(".env")
    if env_path.exists():
        print("✅ .env file already exists")
        return True
        
    print("📝 Creating .env file...")
    
    env_content = """# Database Configuration
DB_NAME=kforce
DB_USER=postgres
DB_PASS=kforce
DB_HOST=localhost
DB_PORT=5444

# API Keys
GROK_API_KEY=********************************************************

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
EMBEDDING_MODEL=nomic-ai/nomic-embed-text-v1
"""
    
    with open(".env", "w") as f:
        f.write(env_content)
        
    print("✅ .env file created")
    return True

def main():
    """Main setup function"""
    print("🚀 Medical Document Search System Setup")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Environment File", create_env_file),
        ("Dependencies", install_requirements),
        ("Ollama Service", check_ollama),
        ("PostgreSQL", check_postgresql),
        ("Grok API", check_grok_api),
        ("PDF Documents", check_diseases_folder),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n{name}:")
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 50)
    print("📋 Setup Summary:")
    
    all_passed = True
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 All checks passed! You can now run:")
        print("  python main_app.py --setup    # Process PDFs and setup database")
        print("  python main_app.py           # Start search interface")
    else:
        print("⚠️  Some checks failed. Please fix the issues above before proceeding.")
        
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
