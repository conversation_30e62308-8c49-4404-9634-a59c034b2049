#!/usr/bin/env python3
"""
Example client for the Document Search API
Demonstrates how to interact with the Flask API
"""

import requests
import json
import time
from typing import List, Dict, Optional

class DocumentSearchClient:
    """Client for interacting with the Document Search API"""
    
    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def get_status(self) -> Dict:
        """Get system status and statistics"""
        try:
            response = self.session.get(f"{self.base_url}/api/status")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"success": False, "error": str(e)}
    
    def health_check(self) -> Dict:
        """Perform health check"""
        try:
            response = self.session.get(f"{self.base_url}/api/health")
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"status": "error", "error": str(e)}
    
    def search(self, query: str, top_k: int = 10, include_llm: bool = True) -> Dict:
        """Search documents using GET request"""
        try:
            params = {
                'q': query,
                'top_k': top_k,
                'include_llm': str(include_llm).lower()
            }
            
            response = self.session.get(f"{self.base_url}/api/search", params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"success": False, "error": str(e)}
    
    def search_post(self, query: str, top_k: int = 10, include_llm: bool = True) -> Dict:
        """Search documents using POST request"""
        try:
            payload = {
                "query": query,
                "top_k": top_k,
                "include_llm": include_llm
            }
            
            response = self.session.post(
                f"{self.base_url}/api/search",
                json=payload,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            return {"success": False, "error": str(e)}
    
    def search_vector_only(self, query: str, top_k: int = 10) -> List[Dict]:
        """Search documents without LLM response (faster)"""
        result = self.search(query, top_k=top_k, include_llm=False)
        if result.get("success"):
            return result.get("results", [])
        return []
    
    def search_with_ai(self, query: str, top_k: int = 10) -> Dict:
        """Search documents with AI response"""
        return self.search(query, top_k=top_k, include_llm=True)

def demo_basic_usage():
    """Demonstrate basic API usage"""
    print("🔍 Document Search API Client Demo")
    print("=" * 50)
    
    # Initialize client
    client = DocumentSearchClient()
    
    # 1. Health check
    print("1. Health Check")
    health = client.health_check()
    print(f"   Status: {health.get('status', 'unknown')}")
    
    # 2. System status
    print("\n2. System Status")
    status = client.get_status()
    if status.get("success"):
        print(f"   Total Documents: {status.get('total_chunks', 0):,} chunks")
        print(f"   Unique Files: {status.get('unique_files', 0):,}")
        print(f"   Average Chunk Size: {status.get('avg_chunk_size', 0)} words")
    else:
        print(f"   Error: {status.get('error')}")
    
    # 3. Simple vector search
    print("\n3. Vector Search (Fast)")
    query = "payment amount"
    results = client.search_vector_only(query, top_k=3)
    print(f"   Query: '{query}'")
    print(f"   Found {len(results)} results:")
    for i, result in enumerate(results, 1):
        print(f"     {i}. {result.get('filename')} - {result.get('relevance', 0):.1f}%")
    
    # 4. Search with AI response
    print("\n4. Search with AI Response")
    query = "service cost details"
    print(f"   Query: '{query}'")
    print("   Generating AI response...")
    
    start_time = time.time()
    ai_result = client.search_with_ai(query, top_k=5)
    end_time = time.time()
    
    if ai_result.get("success"):
        print(f"   Response time: {end_time - start_time:.2f}s")
        print(f"   Found {ai_result.get('total_results', 0)} documents")
        
        # Show AI response preview
        ai_response = ai_result.get('llm_response', '')
        if ai_response:
            preview = ai_response[:200] + "..." if len(ai_response) > 200 else ai_response
            print(f"   AI Response: {preview}")
    else:
        print(f"   Error: {ai_result.get('error')}")

def demo_advanced_usage():
    """Demonstrate advanced API usage"""
    print("\n🚀 Advanced Usage Examples")
    print("=" * 50)
    
    client = DocumentSearchClient()
    
    # Multiple queries with performance tracking
    queries = [
        "payment invoice total",
        "service cost breakdown",
        "medicine cost details",
        "customer information",
        "invoice number"
    ]
    
    print("Performance Test - Vector Search Only:")
    total_time = 0
    for i, query in enumerate(queries, 1):
        start_time = time.time()
        results = client.search_vector_only(query, top_k=3)
        end_time = time.time()
        
        query_time = end_time - start_time
        total_time += query_time
        
        print(f"  {i}. '{query}': {len(results)} results in {query_time:.3f}s")
    
    print(f"\nTotal time: {total_time:.3f}s")
    print(f"Average time per query: {total_time/len(queries):.3f}s")

def demo_error_handling():
    """Demonstrate error handling"""
    print("\n⚠️  Error Handling Examples")
    print("=" * 50)
    
    client = DocumentSearchClient()
    
    # Test various error conditions
    error_tests = [
        ("Empty query", ""),
        ("Very long query", "a" * 1000),
        ("Special characters", "!@#$%^&*()"),
        ("Invalid top_k", None)  # Will be handled in the request
    ]
    
    for test_name, query in error_tests:
        print(f"\nTesting: {test_name}")
        if query is not None:
            result = client.search(query, top_k=5, include_llm=False)
        else:
            # Test invalid top_k by making raw request
            try:
                response = requests.get(f"{client.base_url}/api/search?q=test&top_k=invalid")
                result = response.json() if response.headers.get('content-type', '').startswith('application/json') else {"success": False, "error": "Invalid response"}
            except Exception as e:
                result = {"success": False, "error": str(e)}
        
        if result.get("success"):
            print(f"  ✅ Handled successfully: {len(result.get('results', []))} results")
        else:
            print(f"  ❌ Error (expected): {result.get('error', 'Unknown error')}")

def interactive_search():
    """Interactive search session"""
    print("\n💬 Interactive Search Session")
    print("=" * 50)
    print("Enter search queries (type 'quit' to exit)")
    
    client = DocumentSearchClient()
    
    while True:
        try:
            query = input("\n🔍 Search query: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                print("Please enter a search query.")
                continue
            
            # Ask for search type
            search_type = input("Search type (1=fast/vector, 2=with AI) [1]: ").strip()
            include_llm = search_type == "2"
            
            print(f"🔍 Searching for: '{query}'...")
            
            start_time = time.time()
            result = client.search(query, top_k=5, include_llm=include_llm)
            end_time = time.time()
            
            if result.get("success"):
                print(f"⏱️  Response time: {end_time - start_time:.2f}s")
                print(f"📋 Found {result.get('total_results', 0)} results:")
                
                # Show results
                for i, doc in enumerate(result.get('results', []), 1):
                    print(f"  {i}. {doc.get('filename')} ({doc.get('relevance', 0):.1f}%)")
                    print(f"     {doc.get('content', '')[:100]}...")
                
                # Show AI response if available
                if result.get('llm_response'):
                    print(f"\n🤖 AI Response:")
                    print(f"   {result['llm_response'][:300]}...")
            else:
                print(f"❌ Search failed: {result.get('error')}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main function to run all demos"""
    print("🚀 Document Search API Client Examples")
    print("=" * 60)
    
    # Check if API is available
    client = DocumentSearchClient()
    health = client.health_check()
    
    if health.get("status") != "healthy":
        print("❌ API is not available. Please start the Flask server first:")
        print("   python3 flask_search_api.py")
        return
    
    print("✅ API is available and healthy!")
    
    # Run demos
    demo_basic_usage()
    demo_advanced_usage()
    demo_error_handling()
    
    # Ask if user wants interactive session
    print("\n" + "=" * 60)
    interactive = input("Start interactive search session? (y/N): ").strip().lower()
    if interactive in ['y', 'yes']:
        interactive_search()
    
    print("\n🎉 Demo completed!")
    print(f"📚 API Documentation: http://localhost:5001")
    print(f"🔍 Web Interface: http://localhost:5001")

if __name__ == "__main__":
    main()
