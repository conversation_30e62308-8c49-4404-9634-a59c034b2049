#!/usr/bin/env python3
"""
Production startup script for Render deployment
"""

import os
import sys
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_environment():
    """Check if all required environment variables are set"""
    
    print("🔍 Checking Environment Variables...")
    
    required_vars = [
        'DB_HOST',
        'DB_USER', 
        'DB_PASS',
        'DB_NAME'
    ]
    
    optional_vars = [
        'DATABASE_URL',
        'GROQ_API_KEY',
        'XAI_API_KEY',
        'FLASK_HOST',
        'FLASK_PORT',
        'PORT'
    ]
    
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
            print(f"   ❌ {var}: Not set")
        else:
            print(f"   ✅ {var}: Set")
    
    for var in optional_vars:
        if os.getenv(var):
            print(f"   ✅ {var}: Set")
        else:
            print(f"   ⚠️  {var}: Not set (optional)")
    
    if missing_vars:
        print(f"\n❌ Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ All required environment variables are set")
    return True

def test_database_connection():
    """Test database connection before starting the app"""
    
    print("\n🔍 Testing Database Connection...")
    
    try:
        import psycopg2
        
        # Try DATABASE_URL first (preferred for production)
        database_url = os.getenv("DATABASE_URL")
        
        if database_url:
            print("   Using DATABASE_URL for connection...")
            conn = psycopg2.connect(database_url)
        else:
            print("   Using individual parameters for connection...")
            conn = psycopg2.connect(
                dbname=os.getenv("DB_NAME"),
                user=os.getenv("DB_USER"),
                password=os.getenv("DB_PASS"),
                host=os.getenv("DB_HOST"),
                port=os.getenv("DB_PORT", "5432")
            )
        
        # Test the connection
        with conn.cursor() as cur:
            cur.execute("SELECT 1")
            cur.fetchone()
        
        conn.close()
        print("✅ Database connection successful")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def start_application():
    """Start the Flask application"""
    
    print("\n🚀 Starting Flask Application...")
    
    # Import and run the Flask app
    try:
        from flask_search_api import app
        
        # Get configuration
        host = os.getenv('FLASK_HOST', '0.0.0.0')
        port = int(os.getenv('PORT', os.getenv('FLASK_PORT', 5000)))
        debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        
        print(f"   Host: {host}")
        print(f"   Port: {port}")
        print(f"   Debug: {debug}")
        print(f"   Environment: {os.getenv('FLASK_ENV', 'development')}")
        
        # Start the app
        app.run(host=host, port=port, debug=debug)
        
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    
    print("🏥 Medical Document Search API - Production Startup")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        print("\n💥 Environment check failed. Please set required variables.")
        sys.exit(1)
    
    # Test database connection with retries
    max_retries = 3
    for attempt in range(max_retries):
        if test_database_connection():
            break
        else:
            if attempt < max_retries - 1:
                print(f"   ⏳ Retrying in 5 seconds... (attempt {attempt + 2}/{max_retries})")
                time.sleep(5)
            else:
                print(f"\n💥 Database connection failed after {max_retries} attempts.")
                print("   The application will start but may not function properly.")
                print("   Please check your database configuration.")
    
    # Start the application
    start_application()

if __name__ == "__main__":
    main()
