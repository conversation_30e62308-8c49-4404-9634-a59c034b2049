#!/usr/bin/env python3
"""
Test script for Flask Search API
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:5001"

def test_health_endpoint():
    """Test the health check endpoint"""
    print("🏥 Testing Health Endpoint")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_status_endpoint():
    """Test the status endpoint"""
    print("\n📊 Testing Status Endpoint")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/api/status")
        print(f"Status Code: {response.status_code}")
        data = response.json()
        print(f"Response: {json.dumps(data, indent=2)}")
        
        if response.status_code == 200 and data.get('success'):
            print(f"✅ System Status: {data.get('status')}")
            print(f"📄 Total Chunks: {data.get('total_chunks'):,}")
            print(f"📁 Unique Files: {data.get('unique_files'):,}")
            print(f"📝 Avg Chunk Size: {data.get('avg_chunk_size')} words")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_search_get():
    """Test search with GET request"""
    print("\n🔍 Testing Search (GET)")
    print("-" * 30)
    
    try:
        # Test search query
        query = "payment invoice amount"
        params = {
            'q': query,
            'top_k': 5,
            'include_llm': 'true'
        }
        
        print(f"Query: {query}")
        response = requests.get(f"{BASE_URL}/api/search", params=params)
        print(f"Status Code: {response.status_code}")
        
        data = response.json()
        
        if response.status_code == 200 and data.get('success'):
            print(f"✅ Search successful!")
            print(f"📋 Found {data.get('total_results')} results")
            
            # Show first result
            if data.get('results'):
                first_result = data['results'][0]
                print(f"🔝 Top result: {first_result.get('filename')}")
                print(f"   Relevance: {first_result.get('relevance'):.1f}%")
                print(f"   Heading: {first_result.get('heading')}")
            
            # Show LLM response preview
            if data.get('llm_response'):
                llm_preview = data['llm_response'][:200] + "..." if len(data['llm_response']) > 200 else data['llm_response']
                print(f"🤖 LLM Response: {llm_preview}")
        else:
            print(f"❌ Search failed: {data.get('error')}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_search_post():
    """Test search with POST request"""
    print("\n🔍 Testing Search (POST)")
    print("-" * 30)
    
    try:
        # Test search query
        payload = {
            "query": "service cost details",
            "top_k": 3,
            "include_llm": False  # Test without LLM for faster response
        }
        
        print(f"Query: {payload['query']}")
        response = requests.post(
            f"{BASE_URL}/api/search",
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        print(f"Status Code: {response.status_code}")
        
        data = response.json()
        
        if response.status_code == 200 and data.get('success'):
            print(f"✅ Search successful!")
            print(f"📋 Found {data.get('total_results')} results")
            
            # Show all results
            for i, result in enumerate(data.get('results', []), 1):
                print(f"   {i}. {result.get('filename')} - {result.get('relevance'):.1f}%")
        else:
            print(f"❌ Search failed: {data.get('error')}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_error_handling():
    """Test error handling"""
    print("\n❌ Testing Error Handling")
    print("-" * 30)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Empty query
    try:
        response = requests.get(f"{BASE_URL}/api/search?q=")
        if response.status_code == 400:
            print("✅ Empty query handled correctly")
            tests_passed += 1
        else:
            print("❌ Empty query not handled correctly")
    except Exception as e:
        print(f"❌ Error testing empty query: {e}")
    
    # Test 2: Invalid top_k
    try:
        response = requests.get(f"{BASE_URL}/api/search?q=test&top_k=100")
        if response.status_code == 400:
            print("✅ Invalid top_k handled correctly")
            tests_passed += 1
        else:
            print("❌ Invalid top_k not handled correctly")
    except Exception as e:
        print(f"❌ Error testing invalid top_k: {e}")
    
    # Test 3: Non-existent endpoint
    try:
        response = requests.get(f"{BASE_URL}/api/nonexistent")
        if response.status_code == 404:
            print("✅ 404 error handled correctly")
            tests_passed += 1
        else:
            print("❌ 404 error not handled correctly")
    except Exception as e:
        print(f"❌ Error testing 404: {e}")
    
    return tests_passed == total_tests

def run_performance_test():
    """Run a simple performance test"""
    print("\n⚡ Performance Test")
    print("-" * 30)
    
    try:
        queries = [
            "payment amount",
            "service cost",
            "invoice total",
            "medicine cost",
            "customer details"
        ]
        
        total_time = 0
        successful_requests = 0
        
        for query in queries:
            start_time = time.time()
            
            try:
                response = requests.get(
                    f"{BASE_URL}/api/search",
                    params={'q': query, 'top_k': 5, 'include_llm': 'false'},
                    timeout=10
                )
                
                end_time = time.time()
                request_time = end_time - start_time
                total_time += request_time
                
                if response.status_code == 200:
                    successful_requests += 1
                    print(f"✅ '{query}': {request_time:.2f}s")
                else:
                    print(f"❌ '{query}': Failed ({response.status_code})")
                    
            except Exception as e:
                print(f"❌ '{query}': Error - {e}")
        
        if successful_requests > 0:
            avg_time = total_time / successful_requests
            print(f"\n📊 Performance Summary:")
            print(f"   Successful requests: {successful_requests}/{len(queries)}")
            print(f"   Average response time: {avg_time:.2f}s")
            print(f"   Total time: {total_time:.2f}s")
        
        return successful_requests == len(queries)
        
    except Exception as e:
        print(f"❌ Performance test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Flask Search API Test Suite")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    tests = [
        ("Health Check", test_health_endpoint),
        ("Status Endpoint", test_status_endpoint),
        ("Search GET", test_search_get),
        ("Search POST", test_search_post),
        ("Error Handling", test_error_handling),
        ("Performance", run_performance_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Flask API is working perfectly!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print(f"\n🌐 API is running at: {BASE_URL}")
    print(f"📚 Web interface: {BASE_URL}")
    print(f"🔍 Search endpoint: {BASE_URL}/api/search")
    print(f"📊 Status endpoint: {BASE_URL}/api/status")

if __name__ == "__main__":
    main()
