#!/usr/bin/env python3
"""
Flask API for Document Search System
Provides REST API endpoints for document search functionality
"""

from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv
from search_system import MedicalSearchSystem

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize search system
search_system = None

def initialize_search_system():
    """Initialize the search system"""
    global search_system
    try:
        search_system = MedicalSearchSystem()
        # Store table name for use in API
        search_system.table_name = os.getenv("DB_TABLE_NAME", "documents")
        return True
    except Exception as e:
        print(f"Error initializing search system: {e}")
        return False

# HTML template for the web interface
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Search API</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .search-box { margin: 20px 0; }
        .search-box input { width: 70%; padding: 10px; font-size: 16px; border: 1px solid #ddd; border-radius: 4px; }
        .search-box button { padding: 10px 20px; font-size: 16px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .search-box button:hover { background: #2980b9; }
        .results { margin-top: 20px; }
        .result-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #3498db; }
        .result-header { font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .result-meta { color: #7f8c8d; font-size: 14px; margin-bottom: 10px; }
        .result-content { line-height: 1.6; }
        .loading { text-align: center; color: #7f8c8d; }
        .error { background: #e74c3c; color: white; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .stats { background: #ecf0f1; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .api-docs { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 30px; }
        .endpoint { background: white; padding: 15px; margin: 10px 0; border-radius: 4px; border-left: 4px solid #27ae60; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Document Search API</h1>
        <p>Advanced document search with AI-powered responses</p>
    </div>

    <div class="stats" id="stats">
        <h3>📊 System Status</h3>
        <p>Loading system information...</p>
    </div>

    <div class="search-box">
        <h3>🔍 Search Documents</h3>
        <input type="text" id="searchQuery" placeholder="Enter your search query..." onkeypress="handleKeyPress(event)">
        <button onclick="performSearch()">Search</button>
    </div>

    <div class="results" id="results"></div>

    <div class="api-docs">
        <h3>📚 API Documentation</h3>
        
        <div class="endpoint">
            <h4>GET /api/search</h4>
            <p><strong>Parameters:</strong></p>
            <ul>
                <li><code>q</code> (required): Search query</li>
                <li><code>top_k</code> (optional): Number of results (default: 10)</li>
                <li><code>include_llm</code> (optional): Include AI response (default: true)</li>
            </ul>
            <p><strong>Example:</strong> <code>/api/search?q=payment%20invoice&top_k=5</code></p>
        </div>

        <div class="endpoint">
            <h4>GET /api/status</h4>
            <p>Get system status and database statistics</p>
        </div>

        <div class="endpoint">
            <h4>POST /api/search</h4>
            <p><strong>Body:</strong> <code>{"query": "search terms", "top_k": 10, "include_llm": true}</code></p>
        </div>
    </div>

    <script>
        // Load system status on page load
        window.onload = function() {
            loadSystemStatus();
        };

        function loadSystemStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('stats').innerHTML = `
                            <h3>📊 System Status</h3>
                            <p><strong>Status:</strong> ${data.status}</p>
                            <p><strong>Total Documents:</strong> ${data.total_chunks.toLocaleString()} chunks from ${data.unique_files.toLocaleString()} files</p>
                            <p><strong>Average Chunk Size:</strong> ${data.avg_chunk_size} words</p>
                            <p><strong>Last Updated:</strong> ${new Date().toLocaleString()}</p>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('stats').innerHTML = `
                        <h3>📊 System Status</h3>
                        <p style="color: red;">Error loading system status</p>
                    `;
                });
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                performSearch();
            }
        }

        function performSearch() {
            const query = document.getElementById('searchQuery').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }

            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="loading">🔍 Searching documents...</div>';

            fetch(`/api/search?q=${encodeURIComponent(query)}&top_k=10&include_llm=true`)
                .then(response => response.json())
                .then(data => {
                    displayResults(data, query);
                })
                .catch(error => {
                    resultsDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                });
        }

        function displayResults(data, query) {
            const resultsDiv = document.getElementById('results');
            
            if (!data.success) {
                resultsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                return;
            }

            let html = `<h3>🔍 Search Results for "${query}"</h3>`;
            
            if (data.llm_response) {
                html += `
                    <div class="result-item" style="border-left-color: #e74c3c;">
                        <div class="result-header">🤖 AI Analysis</div>
                        <div class="result-content">${data.llm_response.replace(/\\n/g, '<br>')}</div>
                    </div>
                `;
            }

            html += `<h4>📚 Source Documents (${data.results.length} found)</h4>`;

            data.results.forEach((result, index) => {
                html += `
                    <div class="result-item">
                        <div class="result-header">${index + 1}. ${result.filename}</div>
                        <div class="result-meta">
                            📄 Section: ${result.heading} | 
                            📊 Relevance: ${result.relevance.toFixed(1)}% | 
                            📝 Words: ${result.word_count}
                        </div>
                        <div class="result-content">${result.content.substring(0, 300)}...</div>
                    </div>
                `;
            });

            if (data.results.length === 0) {
                html += '<div class="result-item">No documents found for your query.</div>';
            }

            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Serve the web interface"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get system status and statistics"""
    try:
        if not search_system:
            return jsonify({
                'success': False,
                'error': 'Search system not initialized'
            }), 500

        # Get database statistics
        import psycopg2
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "krushal"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Get table name from search system
            table_name = search_system.search_engine.db_manager.table_name

            cur.execute(f"SELECT COUNT(*) FROM {table_name}")
            total_chunks = cur.fetchone()[0]

            cur.execute(f"SELECT COUNT(DISTINCT filename) FROM {table_name}")
            unique_files = cur.fetchone()[0]

            cur.execute(f"SELECT AVG(word_count) FROM {table_name}")
            avg_chunk_size = cur.fetchone()[0] or 0
        
        conn.close()

        return jsonify({
            'success': True,
            'status': 'operational',
            'total_chunks': total_chunks,
            'unique_files': unique_files,
            'avg_chunk_size': round(avg_chunk_size, 1),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/search', methods=['GET', 'POST'])
def search_documents():
    """Search documents endpoint"""
    try:
        if not search_system:
            return jsonify({
                'success': False,
                'error': 'Search system not initialized'
            }), 500

        # Get parameters
        if request.method == 'GET':
            query = request.args.get('q', '').strip()
            top_k = int(request.args.get('top_k', 10))
            include_llm = request.args.get('include_llm', 'true').lower() == 'true'
        else:  # POST
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'error': 'No JSON data provided'
                }), 400
            
            query = data.get('query', '').strip()
            top_k = data.get('top_k', 10)
            include_llm = data.get('include_llm', True)

        # Validate query
        if not query:
            return jsonify({
                'success': False,
                'error': 'Query parameter is required'
            }), 400

        # Validate top_k
        if not isinstance(top_k, int) or top_k < 1 or top_k > 50:
            return jsonify({
                'success': False,
                'error': 'top_k must be an integer between 1 and 50'
            }), 400

        # Perform search
        if include_llm:
            # Use full search with LLM response
            response_data = search_system.search_and_answer(query, top_k=top_k)

            return jsonify({
                'success': True,
                'query': query,
                'results': response_data.get('sources', []),
                'llm_response': response_data.get('answer', ''),
                'total_results': len(response_data.get('sources', [])),
                'timestamp': datetime.now().isoformat()
            })
        else:
            # Use vector search only
            results = search_system.search_engine.search_similar_documents(query, top_k=top_k)

            return jsonify({
                'success': True,
                'query': query,
                'results': results,
                'total_results': len(results),
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc() if app.debug else None
        }), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Basic health check
        status = 'healthy' if search_system else 'unhealthy'
        
        return jsonify({
            'status': status,
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0'
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found',
        'available_endpoints': [
            'GET /',
            'GET /api/status',
            'GET /api/search',
            'POST /api/search',
            'GET /api/health'
        ]
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': 'Please check the server logs for more details'
    }), 500

if __name__ == '__main__':
    print("🚀 Starting Document Search API...")
    
    # Initialize search system
    if initialize_search_system():
        print("✅ Search system initialized successfully")
        
        # Get configuration
        host = os.getenv('FLASK_HOST', '0.0.0.0')
        port = int(os.getenv('FLASK_PORT', 5000))
        debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        
        print(f"🌐 Starting Flask server on http://{host}:{port}")
        print(f"📚 API Documentation available at http://{host}:{port}")
        print(f"🔍 Search interface available at http://{host}:{port}")
        
        # Start the Flask app
        app.run(host=host, port=port, debug=debug)
    else:
        print("❌ Failed to initialize search system")
        print("Please check your database connection and configuration")
