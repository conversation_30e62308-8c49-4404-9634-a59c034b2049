#!/usr/bin/env python3
"""
Main Application for PDF Medical Document Search System
Combines PDF processing, embedding, and search functionality
"""

import argparse
import sys
from pathlib import Path
from pdf_embedder import PDFEmbeddingPipeline
from search_system import MedicalSearchSystem

def setup_database():
    """Setup database and process PDFs"""
    print("🔧 Setting up database and processing PDFs...")
    
    pipeline = PDFEmbeddingPipeline()
    
    # Process diseases folder
    diseases_folder = "diseases"
    if not Path(diseases_folder).exists():
        print(f"❌ Error: {diseases_folder} folder not found!")
        return False
        
    pipeline.process_pdf_folder(diseases_folder)
    return True

def run_search_interface():
    """Run the interactive search interface"""
    print("🚀 Starting Medical Document Search System...")
    
    search_system = MedicalSearchSystem()
    
    # Check if database has documents
    stats = search_system.get_database_stats()
    if "error" in stats or stats.get("total_documents", 0) == 0:
        print("❌ No documents found in database. Please run setup first.")
        print("Usage: python main_app.py --setup")
        return
    
    print("🏥 Medical Document Search System")
    print("=" * 50)
    print(f"📊 Database contains {stats['total_documents']} document chunks from {stats['unique_files']} files")
    print("\nAvailable documents:")
    for file_info in stats['files'][:10]:  # Show first 10 files
        print(f"  • {file_info['filename']} ({file_info['chunks']} chunks)")
    
    if len(stats['files']) > 10:
        print(f"  ... and {len(stats['files']) - 10} more files")
    
    print("\n" + "=" * 50)
    
    while True:
        try:
            # Get user query
            query = input("\n🔍 Enter your medical question (or 'quit' to exit): ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not query:
                continue
                
            # Search and get answer
            print("\n⏳ Searching documents and generating response...")
            result = search_system.search_and_answer(query)
            
            # Display results
            print(f"\n📋 Found {result['documents_found']} relevant documents")
            print("\n🤖 AI Response:")
            print("=" * 50)
            print(result['answer'])
            print("=" * 50)
            
            # Show sources
            if result['sources']:
                print(f"\n📚 Sources used:")
                for i, source in enumerate(result['sources'][:5], 1):
                    print(f"  {i}. {source['filename']} (relevance: {source['similarity_score']:.1%})")
            
            # Ask if user wants to see the raw context
            show_context = input("\n📄 Show raw document context? (y/N): ").strip().lower()
            if show_context in ['y', 'yes']:
                print("\n📄 Raw Context:")
                print("-" * 50)
                print(result['context'][:2000] + "..." if len(result['context']) > 2000 else result['context'])
                print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def test_single_pdf():
    """Test processing a single PDF"""
    print("🧪 Testing single PDF processing...")
    
    # Find first PDF in diseases folder
    diseases_folder = Path("diseases")
    pdf_files = list(diseases_folder.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found in diseases folder")
        return
        
    test_pdf = pdf_files[0]
    print(f"📄 Testing with: {test_pdf.name}")
    
    pipeline = PDFEmbeddingPipeline()
    pipeline.db_manager.create_tables()
    pipeline.process_single_pdf(str(test_pdf))
    
    print("✅ Test completed!")

def show_database_info():
    """Show database information"""
    search_system = MedicalSearchSystem()
    stats = search_system.get_database_stats()
    
    if "error" in stats:
        print(f"❌ Database error: {stats['error']}")
        return
        
    print("📊 Database Information")
    print("=" * 30)
    print(f"Total document chunks: {stats['total_documents']}")
    print(f"Unique files: {stats['unique_files']}")
    print("\nFiles in database:")
    
    for file_info in stats['files']:
        print(f"  • {file_info['filename']} - {file_info['chunks']} chunks")

def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(
        description="Medical Document Search System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_app.py                    # Run interactive search
  python main_app.py --setup           # Setup database and process PDFs
  python main_app.py --test            # Test with single PDF
  python main_app.py --info            # Show database information
        """
    )
    
    parser.add_argument(
        "--setup", 
        action="store_true",
        help="Setup database and process all PDFs in diseases folder"
    )
    
    parser.add_argument(
        "--test",
        action="store_true", 
        help="Test processing with a single PDF"
    )
    
    parser.add_argument(
        "--info",
        action="store_true",
        help="Show database information"
    )
    
    args = parser.parse_args()
    
    if args.setup:
        success = setup_database()
        if success:
            print("✅ Setup completed successfully!")
            print("You can now run the search interface with: python main_app.py")
        else:
            print("❌ Setup failed!")
            sys.exit(1)
            
    elif args.test:
        test_single_pdf()
        
    elif args.info:
        show_database_info()
        
    else:
        # Default: run search interface
        run_search_interface()

if __name__ == "__main__":
    main()
