#!/usr/bin/env python3
"""
Test database connection for deployment troubleshooting
"""

import os
import psycopg2
from dotenv import load_dotenv
import time

# Load environment variables
load_dotenv()

def test_database_connection():
    """Test database connection with detailed error reporting"""
    
    print("🔍 Testing Database Connection...")
    print("=" * 50)
    
    # Get connection parameters
    database_url = os.getenv("DATABASE_URL")
    db_name = os.getenv("DB_NAME", "postgres")
    db_user = os.getenv("DB_USER", "postgres")
    db_pass = os.getenv("DB_PASS")
    db_host = os.getenv("DB_HOST", "localhost")
    db_port = os.getenv("DB_PORT", "5432")
    
    print(f"📊 Connection Parameters:")
    print(f"   DATABASE_URL: {'✅ Set' if database_url else '❌ Not set'}")
    print(f"   DB_HOST: {db_host}")
    print(f"   DB_PORT: {db_port}")
    print(f"   DB_NAME: {db_name}")
    print(f"   DB_USER: {db_user}")
    print(f"   DB_PASS: {'✅ Set' if db_pass else '❌ Not set'}")
    print()
    
    # Test connection methods
    connection_methods = []
    
    if database_url:
        connection_methods.append(("DATABASE_URL", lambda: psycopg2.connect(database_url)))
    
    if all([db_host, db_port, db_name, db_user, db_pass]):
        connection_methods.append((
            "Individual Parameters", 
            lambda: psycopg2.connect(
                dbname=db_name,
                user=db_user,
                password=db_pass,
                host=db_host,
                port=db_port
            )
        ))
        
        # Also test with SSL
        connection_methods.append((
            "Individual Parameters + SSL", 
            lambda: psycopg2.connect(
                dbname=db_name,
                user=db_user,
                password=db_pass,
                host=db_host,
                port=db_port,
                sslmode='require'
            )
        ))
    
    # Test each connection method
    for method_name, connect_func in connection_methods:
        print(f"🔄 Testing {method_name}...")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"   Attempt {attempt + 1}/{max_retries}...")
                
                # Try to connect
                conn = connect_func()
                
                # Test the connection
                with conn.cursor() as cur:
                    cur.execute("SELECT version()")
                    version = cur.fetchone()[0]
                    
                    cur.execute("SELECT current_database()")
                    current_db = cur.fetchone()[0]
                    
                    # Test pgvector extension
                    try:
                        cur.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector'")
                        has_vector = cur.fetchone() is not None
                    except:
                        has_vector = False
                
                conn.close()
                
                print(f"   ✅ {method_name} - SUCCESS!")
                print(f"      Database: {current_db}")
                print(f"      PostgreSQL: {version[:50]}...")
                print(f"      pgvector: {'✅ Available' if has_vector else '❌ Not available'}")
                print()
                break
                
            except Exception as e:
                print(f"   ❌ Attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    print(f"   ⏳ Waiting 2 seconds before retry...")
                    time.sleep(2)
                else:
                    print(f"   💥 {method_name} - FAILED after {max_retries} attempts")
                    print()

def test_table_access():
    """Test access to the documents table"""
    
    print("🔍 Testing Table Access...")
    print("=" * 30)
    
    table_name = os.getenv("DB_TABLE_NAME", "documents")
    print(f"📋 Table name: {table_name}")
    
    try:
        # Use the same connection logic as the main app
        database_url = os.getenv("DATABASE_URL")
        
        if database_url:
            conn = psycopg2.connect(database_url)
        else:
            conn = psycopg2.connect(
                dbname=os.getenv("DB_NAME", "postgres"),
                user=os.getenv("DB_USER", "postgres"),
                password=os.getenv("DB_PASS"),
                host=os.getenv("DB_HOST", "localhost"),
                port=os.getenv("DB_PORT", "5432")
            )
        
        with conn.cursor() as cur:
            # Check if table exists
            cur.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                )
            """, (table_name,))
            
            table_exists = cur.fetchone()[0]
            
            if table_exists:
                # Get table info
                cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cur.fetchone()[0]
                
                cur.execute(f"SELECT COUNT(DISTINCT filename) FROM {table_name}")
                file_count = cur.fetchone()[0]
                
                print(f"   ✅ Table '{table_name}' exists")
                print(f"   📊 Rows: {row_count:,}")
                print(f"   📁 Files: {file_count:,}")
            else:
                print(f"   ❌ Table '{table_name}' does not exist")
        
        conn.close()
        
    except Exception as e:
        print(f"   💥 Table access failed: {e}")

if __name__ == "__main__":
    test_database_connection()
    print()
    test_table_access()
