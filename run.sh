#!/bin/bash

# Medical Document Search System - Quick Start Script

echo "🏥 Medical Document Search System"
echo "================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Check if setup has been run
if [ "$1" = "setup" ]; then
    echo "🔧 Running setup..."
    python3 setup.py
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ Setup completed! Now processing PDFs..."
        python3 main_app.py --setup
    else
        echo "❌ Setup failed. Please check the errors above."
        exit 1
    fi
elif [ "$1" = "test" ]; then
    echo "🧪 Running tests..."
    python3 test_system.py
elif [ "$1" = "info" ]; then
    echo "📊 Database information..."
    python3 main_app.py --info
else
    echo "🚀 Starting search interface..."
    python3 main_app.py
fi
