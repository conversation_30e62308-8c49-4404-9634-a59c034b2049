#!/usr/bin/env python3
"""
Test script to verify dynamic table name functionality
"""

import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_table_name_config():
    """Test that table name configuration works"""
    
    print("🧪 Testing Dynamic Table Name Configuration")
    print("=" * 50)
    
    # Get table name from environment
    table_name = os.getenv("DB_TABLE_NAME", "documents")
    print(f"📋 Table name from env: {table_name}")
    
    # Test database connection
    try:
        conn = psycopg2.connect(
            dbname=os.getenv("DB_NAME", "kforce"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASS", "kforce"),
            host=os.getenv("DB_HOST", "localhost"),
            port=os.getenv("DB_PORT", "5444")
        )
        
        with conn.cursor() as cur:
            # Test table access
            print(f"🔍 Testing access to table: {table_name}")
            
            # Get count
            cur.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cur.fetchone()[0]
            print(f"✅ Records in {table_name}: {count:,}")
            
            # Get unique files
            cur.execute(f"SELECT COUNT(DISTINCT filename) FROM {table_name}")
            files = cur.fetchone()[0]
            print(f"✅ Unique files in {table_name}: {files:,}")
            
            # Get average word count
            cur.execute(f"SELECT AVG(word_count) FROM {table_name}")
            avg_words = cur.fetchone()[0]
            print(f"✅ Average words per chunk: {avg_words:.1f}")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_different_table_names():
    """Test with different table name configurations"""
    
    print(f"\n🔄 Testing Different Table Names")
    print("=" * 40)
    
    # List of tables to test
    test_tables = [
        "documents",
        "documents_enhanced", 
        "data_documents",
        "nonexistent_table"
    ]
    
    for table_name in test_tables:
        print(f"\n📋 Testing table: {table_name}")
        
        try:
            conn = psycopg2.connect(
                dbname=os.getenv("DB_NAME", "kforce"),
                user=os.getenv("DB_USER", "postgres"),
                password=os.getenv("DB_PASS", "kforce"),
                host=os.getenv("DB_HOST", "localhost"),
                port=os.getenv("DB_PORT", "5444")
            )
            
            with conn.cursor() as cur:
                cur.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cur.fetchone()[0]
                print(f"   ✅ {table_name}: {count:,} records")
                
            conn.close()
            
        except Exception as e:
            print(f"   ❌ {table_name}: {str(e)[:50]}...")

def test_search_system_with_table_name():
    """Test search system with dynamic table name"""
    
    print(f"\n🔍 Testing Search System with Dynamic Table Name")
    print("=" * 50)
    
    try:
        from search_system import VectorSearchEngine
        
        # Create search engine
        search_engine = VectorSearchEngine()
        
        # Check table name
        table_name = search_engine.db_manager.table_name
        print(f"📋 Search engine table name: {table_name}")
        
        # Test search
        results = search_engine.search_similar_documents("payment", top_k=3)
        print(f"✅ Search results: {len(results)} documents found")
        
        if results:
            first_result = results[0]
            print(f"   Top result: {first_result.get('filename')}")
            print(f"   Relevance: {first_result.get('relevance', 0):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Search system error: {e}")
        return False

def test_flask_api_table_name():
    """Test Flask API with table name"""
    
    print(f"\n🌐 Testing Flask API Table Name")
    print("=" * 40)
    
    try:
        import requests
        
        # Test status endpoint
        response = requests.get("http://localhost:5001/api/status", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Status: {data.get('status')}")
            print(f"✅ Total chunks: {data.get('total_chunks', 0):,}")
            print(f"✅ Unique files: {data.get('unique_files', 0):,}")
            return True
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   Response: {response.text[:100]}...")
            return False
            
    except Exception as e:
        print(f"❌ API connection error: {e}")
        return False

def main():
    """Run all tests"""
    
    print("🧪 DYNAMIC TABLE NAME CONFIGURATION TESTS")
    print("=" * 60)
    
    tests = [
        ("Table Name Config", test_table_name_config),
        ("Different Table Names", test_different_table_names),
        ("Search System", test_search_system_with_table_name),
        ("Flask API", test_flask_api_table_name)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Dynamic table name is working correctly!")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    # Show current configuration
    print(f"\n📋 Current Configuration:")
    print(f"   DB_NAME: {os.getenv('DB_NAME')}")
    print(f"   DB_TABLE_NAME: {os.getenv('DB_TABLE_NAME')}")
    print(f"   DB_HOST: {os.getenv('DB_HOST')}")
    print(f"   DB_PORT: {os.getenv('DB_PORT')}")

if __name__ == "__main__":
    main()
