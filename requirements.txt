aiohappyeyeballs==2.4.4
aiohttp==3.10.11
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.5.2
appnope==0.1.4
argon2-cffi==25.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.4
async-timeout==5.0.1
asyncpg==0.29.0
attrs==25.3.0
babel==2.17.0
backcall==0.2.0
beautifulsoup4==4.13.4
bleach==6.1.0
blinker==1.8.2
boto3==1.37.38
botocore==1.37.38
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
ci-info==0.3.0
click==8.1.8
comm==0.2.2
configobj==5.0.9
configparser==7.1.0
contourpy==1.1.1
cycler==0.12.1
dataclasses-json==0.6.7
debugpy==1.8.14
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
dotenv==0.9.9
einops==0.8.1
etelemetry==0.3.1
exceptiongroup==1.3.0
executing==2.2.0
fastjsonschema==2.21.1
filelock==3.16.1
filetype==1.2.0
PyMuPDF==1.24.11
flask==3.0.3
Flask-Cors==5.0.0
fonttools==4.57.0
fqdn==1.5.1
frozenlist==1.5.0
fsspec==2025.3.0
greenlet==3.1.1
h11==0.16.0
hf-xet==1.1.2
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
huggingface-hub==0.32.3
idna==3.10
importlib-metadata==8.5.0
importlib-resources==6.4.5
ipykernel==6.29.5
ipython==8.2.0
ipywidgets==8.1.7
isodate==0.6.1
isoduration==20.11.0
itsdangerous==2.2.0
jedi==0.19.2
jinja2==3.1.6
jiter==0.9.1
jmespath==1.0.1
joblib==1.4.2
json5==0.12.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2023.12.1
jupyter==1.0.0
jupyter-client==8.6.3
jupyter-console==6.6.3
jupyter-core==5.8.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter-server==2.14.2
jupyter-server-terminals==0.5.3
jupyterlab==4.3.7
jupyterlab-pygments==0.3.0
jupyterlab-server==2.27.3
jupyterlab-widgets==3.0.15
kiwisolver==1.4.7
llama-cloud==0.1.23
llama-index==0.10.57
llama-index-agent-openai==0.2.9
llama-index-cli==0.1.13
llama-index-core==0.10.57
llama-index-embeddings-huggingface==0.2.2
llama-index-embeddings-openai==0.1.11
llama-index-indices-managed-llama-cloud==0.2.7
llama-index-legacy==0.9.48.post4
llama-index-llms-ollama==0.1.6
llama-index-llms-openai==0.1.31
llama-index-multi-modal-llms-openai==0.1.9
llama-index-program-openai==0.1.7
llama-index-question-gen-openai==0.1.3
llama-index-readers-file==0.1.33
llama-index-readers-llama-parse==0.1.6
llama-index-vector-stores-postgres==0.1.14
llama-parse==0.4.9
llamaindex-py-client==0.1.19
looseversion==1.3.0
lxml==5.4.0
MarkupSafe==2.1.5
marshmallow==3.22.0
matplotlib==3.7.5
matplotlib-inline==0.1.7
mistune==3.1.3
mpmath==1.3.0
multidict==6.1.0
mypy-extensions==1.1.0
nbclient==0.10.1
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.1
nibabel==5.2.1
nipype==1.8.6
nltk==3.9.1
notebook==7.3.3
notebook-shim==0.2.4
numpy==1.24.4
ollama==0.5.0
openai==1.84.0
overrides==7.7.0
packaging==25.0
pandas==2.0.3
pandocfilters==1.5.1
parso==0.8.4
pathlib==1.0.1
pexpect==4.9.0
pgvector==0.2.5
pickleshare==0.7.5
pillow==10.4.0
pkgutil-resolve-name==1.3.10
platformdirs==4.3.6
prometheus-client==0.21.1
prompt-toolkit==3.0.51
propcache==0.2.0
prov==2.0.1
psutil==7.0.0
psycopg2-binary==2.9.10
ptyprocess==0.7.0
pure-eval==0.2.3
pycparser==2.22
pydantic==2.10.6
pydantic-core==2.27.2
pydot==4.0.0
pygments==2.19.1
PyMuPDF==1.24.11
pyparsing==3.1.4
pypdf==4.3.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==3.3.0
pytz==2025.2
pyxnat==1.6.3
PyYAML==6.0.2
pyzmq==26.4.0
qtconsole==5.6.1
QtPy==2.4.3
rdflib==6.3.2
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rpds-py==0.20.1
s3transfer==0.11.5
safetensors==0.5.3
scikit-learn==1.3.2
scipy==1.10.1
Send2Trash==1.8.3
sentence-transformers==3.2.1
simplejson==3.20.1
six==1.17.0
sniffio==1.3.1
soupsieve==2.7
sqlalchemy==2.0.41
stack-data==0.6.3
striprtf==0.0.26
sympy==1.13.3
tenacity==8.5.0
terminado==0.18.1
threadpoolctl==3.5.0
tiktoken==0.7.0
tinycss2==1.2.1
tokenizers==0.20.3
tomli==2.2.1
torch==2.4.1
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
traits==6.3.2
transformers==4.46.3
types-python-dateutil==2.9.0.20241206
typing-extensions==4.13.2
typing-inspect==0.9.0
tzdata==2025.2
uri-template==1.3.0
urllib3==1.26.20
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
websocket-client==1.8.0
werkzeug==3.0.6
widgetsnbextension==4.0.14
wrapt==1.17.2
yarl==1.15.2
zipp==3.20.2
