# 🔍 Document Search API Documentation

## Overview

The Document Search API provides a Flask-based REST API for searching through medical documents using advanced vector similarity search and AI-powered responses. The system uses PostgreSQL for storage, Ollama for embeddings, and Groq for LLM responses.

## 🚀 Quick Start

### Starting the API Server

```bash
cd /Users/<USER>/krushal/ragV2
python3 flask_search_api.py
```

The API will be available at: `http://localhost:5001`

### Configuration

Configure the API through environment variables in `.env`:

```env
# Flask API Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=5001
FLASK_DEBUG=False

# Database Configuration
DB_NAME=krushal
DB_USER=postgres
DB_PASS=kforce
DB_HOST=localhost
DB_PORT=5444

# LLM Configuration
GROQ_API_KEY=your_groq_api_key_here
XAI_API_KEY=your_xai_api_key_here

# Embedding Configuration
OLLAMA_BASE_URL=http://localhost:11434
EMBEDDING_MODEL=nomic-ai/nomic-embed-text-v1
```

## 📚 API Endpoints

### 1. Web Interface

**GET /**

- **Description**: Serves the interactive web interface for document search
- **Response**: HTML page with search functionality and API documentation
- **Example**: `http://localhost:5001/`

### 2. System Status

**GET /api/status**

- **Description**: Get system status and database statistics
- **Response Format**:

```json
{
  "success": true,
  "status": "operational",
  "total_chunks": 13861,
  "unique_files": 9271,
  "avg_chunk_size": 655.3,
  "timestamp": "2025-06-06T17:16:35.417033"
}
```

### 3. Health Check

**GET /api/health**

- **Description**: Simple health check endpoint
- **Response Format**:

```json
{
  "status": "healthy",
  "timestamp": "2025-06-06T17:16:35.294672",
  "version": "1.0.0"
}
```

### 4. Document Search (GET)

**GET /api/search**

**Parameters:**

- `q` (required): Search query string
- `top_k` (optional): Number of results to return (default: 10, max: 50)
- `include_llm` (optional): Include AI response (default: true)

**Example Requests:**

```bash
# Basic search
curl "http://localhost:5001/api/search?q=payment%20invoice"

# Search with specific parameters
curl "http://localhost:5001/api/search?q=service%20cost&top_k=5&include_llm=false"

# Search with AI response
curl "http://localhost:5001/api/search?q=payment%20amount&include_llm=true"
```

**Response Format:**

```json
{
  "success": true,
  "query": "payment amount",
  "results": [
    {
      "filename": "17220059602445ba316767ca0434c.pdf",
      "heading": "106007557764",
      "content": "Service Details Description Cost Service Cost ₹ 150 Medicine Cost ₹ 100...",
      "word_count": 45,
      "relevance": 61.2
    }
  ],
  "llm_response": "The payment amounts in the documents show two distinct totals: ₹250 and ₹273...",
  "total_results": 10,
  "timestamp": "2025-06-06T17:16:35.417033"
}
```

### 5. Document Search (POST)

**POST /api/search**

**Request Body:**

```json
{
  "query": "payment invoice amount",
  "top_k": 10,
  "include_llm": true
}
```

**Example Request:**

```bash
curl -X POST http://localhost:5001/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "service cost details",
    "top_k": 5,
    "include_llm": false
  }'
```

**Response Format:** Same as GET request

## 🔧 Response Fields

### Search Result Object

- `filename`: Name of the source PDF file
- `heading`: Document section or heading
- `content`: Relevant text content from the document
- `word_count`: Number of words in the chunk
- `relevance`: Similarity score as percentage (0-100)

### API Response Object

- `success`: Boolean indicating if request was successful
- `query`: The search query that was processed
- `results`: Array of search result objects
- `llm_response`: AI-generated response (if include_llm=true)
- `total_results`: Number of results returned
- `timestamp`: ISO timestamp of the response

## ⚠️ Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": "Error description",
  "traceback": "Detailed error trace (debug mode only)"
}
```

### Common Error Codes

- **400 Bad Request**: Invalid parameters (empty query, invalid top_k)
- **404 Not Found**: Endpoint not found
- **500 Internal Server Error**: System error (database connection, LLM API issues)

### Error Examples

```bash
# Empty query
curl "http://localhost:5001/api/search?q="
# Response: 400 - "Query parameter is required"

# Invalid top_k
curl "http://localhost:5001/api/search?q=test&top_k=100"
# Response: 400 - "top_k must be an integer between 1 and 50"

# Non-existent endpoint
curl "http://localhost:5001/api/nonexistent"
# Response: 404 - "Endpoint not found"
```

## 🧪 Testing

### Run API Tests

```bash
python3 test_flask_api.py
```

### Manual Testing Examples

**1. Test System Status:**

```bash
curl http://localhost:5001/api/status
```

**2. Test Simple Search:**

```bash
curl "http://localhost:5001/api/search?q=payment&top_k=3&include_llm=false"
```

**3. Test Search with AI Response:**

```bash
curl "http://localhost:5001/api/search?q=invoice%20total&include_llm=true"
```

**4. Test POST Search:**

```bash
curl -X POST http://localhost:5001/api/search \
  -H "Content-Type: application/json" \
  -d '{"query": "service cost", "top_k": 5}'
```

## 📊 Performance

### Typical Response Times

- **Vector Search Only**: 0.1-0.5 seconds
- **Search with LLM**: 2-5 seconds (depends on LLM API)
- **System Status**: < 0.1 seconds

### Capacity

- **Database**: 13,861+ document chunks from 9,271+ files
- **Concurrent Requests**: Supports multiple concurrent searches
- **Rate Limiting**: None (implement as needed)

## 🔒 Security Considerations

### Current Implementation

- **CORS**: Enabled for all origins (development setup)
- **Authentication**: None (add as needed)
- **Input Validation**: Basic query validation
- **Rate Limiting**: None (implement as needed)

### Production Recommendations

1. Add authentication/authorization
2. Implement rate limiting
3. Configure CORS for specific origins
4. Add request logging and monitoring
5. Use production WSGI server (gunicorn, uwsgi)

## 🚀 Production Deployment

### Using Gunicorn

```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5001 flask_search_api:app
```

### Using Docker

```dockerfile
FROM python:3.8
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5001
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5001", "flask_search_api:app"]
```

### Environment Variables for Production

```env
FLASK_HOST=0.0.0.0
FLASK_PORT=5001
FLASK_DEBUG=False
```

## 📝 Integration Examples

### Python Client

```python
import requests

# Search documents
response = requests.get(
    "http://localhost:5001/api/search",
    params={"q": "payment invoice", "top_k": 5}
)
data = response.json()

if data["success"]:
    print(f"Found {data['total_results']} results")
    for result in data["results"]:
        print(f"- {result['filename']}: {result['relevance']:.1f}%")
```

### JavaScript Client

```javascript
// Search with fetch API
async function searchDocuments(query) {
  const response = await fetch(
    `http://localhost:5001/api/search?q=${encodeURIComponent(query)}&top_k=10`
  );
  const data = await response.json();

  if (data.success) {
    console.log(`Found ${data.total_results} results`);
    return data.results;
  } else {
    console.error("Search failed:", data.error);
    return [];
  }
}
```

## 🆘 Troubleshooting

### Common Issues

**1. Port Already in Use**

```bash
# Change port in .env file
FLASK_PORT=5002
```

**2. Database Connection Error**

```bash
# Check PostgreSQL is running
pg_ctl status

# Verify database credentials in .env
```

**3. LLM API Errors**

```bash
# Check API keys in .env
# Verify Groq API is accessible
```

**4. Embedding Service Error**

```bash
# Check Ollama is running
curl http://localhost:11434/api/tags
```

## 📞 Support

For issues and questions:

1. Check the logs in the Flask terminal
2. Verify all services are running (PostgreSQL, Ollama)
3. Test individual components (database, embeddings, LLM)
4. Review the API test results

The Flask Search API provides a robust, scalable interface for document search with AI-powered responses! 🎉
