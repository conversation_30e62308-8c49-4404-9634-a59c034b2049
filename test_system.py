#!/usr/bin/env python3
"""
Test script for Medical Document Search System
"""

import sys
from pathlib import Path
from pdf_embedder import NomicEmbedder, PDFProcessor, PostgreSQLManager
from search_system import VectorSearchEngine, GrokLLM, MedicalSearchSystem

def test_embedder():
    """Test the Nomic embedder"""
    print("🧪 Testing Nomic Embedder...")
    
    embedder = NomicEmbedder()
    test_texts = [
        "What are the symptoms of acidosis?",
        "Treatment for cattle diseases",
        "Veterinary medicine basics"
    ]
    
    try:
        embeddings = embedder.get_embeddings(test_texts)
        print(f"✅ Generated {len(embeddings)} embeddings")
        print(f"   Embedding dimension: {len(embeddings[0]) if embeddings else 'N/A'}")
        return True
    except Exception as e:
        print(f"❌ Embedder test failed: {e}")
        return False

def test_pdf_processor():
    """Test PDF processing"""
    print("🧪 Testing PDF Processor...")
    
    # Find a test PDF
    diseases_folder = Path("diseases")
    pdf_files = list(diseases_folder.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found for testing")
        return False
    
    test_pdf = pdf_files[0]
    print(f"   Testing with: {test_pdf.name}")
    
    processor = PDFProcessor(chunk_size=500, chunk_overlap=100)
    
    try:
        # Extract text
        text = processor.extract_text_from_pdf(str(test_pdf))
        if not text:
            print("❌ No text extracted from PDF")
            return False
            
        print(f"   Extracted {len(text)} characters")
        
        # Create chunks
        chunks = processor.chunk_text(text, test_pdf.name)
        print(f"   Created {len(chunks)} chunks")
        
        if chunks:
            print(f"   First chunk preview: {chunks[0]['text'][:100]}...")
            
        return len(chunks) > 0
        
    except Exception as e:
        print(f"❌ PDF processor test failed: {e}")
        return False

def test_database():
    """Test database connection and operations"""
    print("🧪 Testing Database Connection...")
    
    db_manager = PostgreSQLManager()
    
    try:
        # Test connection
        with db_manager.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT version()")
                version = cur.fetchone()[0]
                print(f"   PostgreSQL version: {version[:50]}...")
        
        # Test table creation
        db_manager.create_tables()
        print("   ✅ Tables created/verified")
        
        # Test document count
        count = db_manager.get_document_count()
        print(f"   Current document count: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_grok_llm():
    """Test Grok LLM integration"""
    print("🧪 Testing Grok LLM...")
    
    llm = GrokLLM()
    
    test_query = "What is acidosis?"
    test_context = "Acidosis is a condition where the blood pH becomes too low, typically below 7.35. It can be caused by various factors including metabolic disorders."
    
    try:
        response = llm.generate_response(test_query, test_context)
        
        if response and not response.startswith("Error"):
            print("   ✅ Grok LLM responded successfully")
            print(f"   Response preview: {response[:100]}...")
            return True
        else:
            print(f"   ❌ Grok LLM error: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Grok LLM test failed: {e}")
        return False

def test_search_engine():
    """Test vector search engine"""
    print("🧪 Testing Search Engine...")
    
    search_engine = VectorSearchEngine()
    
    try:
        # Test search (may return empty if no documents)
        results = search_engine.search_similar_documents("acidosis symptoms", top_k=3)
        print(f"   Found {len(results)} documents")
        
        if results:
            print(f"   Top result: {results[0]['filename']}")
            print(f"   Similarity: {results[0]['similarity_score']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Search engine test failed: {e}")
        return False

def test_full_pipeline():
    """Test the complete pipeline with a single PDF"""
    print("🧪 Testing Full Pipeline...")
    
    # Find a test PDF
    diseases_folder = Path("diseases")
    pdf_files = list(diseases_folder.glob("*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found for testing")
        return False
    
    test_pdf = pdf_files[0]
    print(f"   Processing: {test_pdf.name}")
    
    try:
        from pdf_embedder import PDFEmbeddingPipeline
        
        pipeline = PDFEmbeddingPipeline()
        
        # Process single PDF
        pipeline.process_single_pdf(str(test_pdf))
        
        # Test search
        search_system = MedicalSearchSystem()
        result = search_system.search_and_answer("What is this document about?", top_k=3)
        
        if result['documents_found'] > 0:
            print(f"   ✅ Pipeline test successful")
            print(f"   Found {result['documents_found']} relevant documents")
            print(f"   Answer preview: {result['answer'][:100]}...")
            return True
        else:
            print("   ⚠️  No documents found in search")
            return False
            
    except Exception as e:
        print(f"❌ Full pipeline test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Medical Document Search System - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Embedder", test_embedder),
        ("PDF Processor", test_pdf_processor),
        ("Database", test_database),
        ("Grok LLM", test_grok_llm),
        ("Search Engine", test_search_engine),
        ("Full Pipeline", test_full_pipeline),
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n{name}:")
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ Test {name} crashed: {e}")
            results.append((name, False))
    
    print("\n" + "=" * 60)
    print("📋 Test Results:")
    
    passed = 0
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Summary: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! System is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
