# 🚀 Deployment Troubleshooting Guide

## Database Connection Issues on Render

### Problem

Getting error: `connection to server at "aws-0-us-east-1.pooler.supabase.com" failed: Network is unreachable`

### Root Causes & Solutions

#### 1. **Supabase Connection Pooling**

Supabase uses connection pooling which can cause issues with direct PostgreSQL connections.

**Solution**: Use the connection pooling URL instead of direct database URL.

In Supabase Dashboard:

- Go to Settings → Database
- Use **Connection Pooling** URL instead of **Direct Connection** URL
- The pooling URL typically looks like: `postgresql://postgres.[project-ref]:[password]@aws-0-[region].pooler.supabase.com:6543/postgres`

#### 2. **SSL Configuration**

Supabase requires SSL connections in production.

**Solution**: Ensure your DATABASE_URL includes `?sslmode=require`:

```
DATABASE_URL=postgresql://postgres:password@host:port/database?sslmode=require
```

#### 3. **Environment Variables**

Make sure all environment variables are properly set in Render.

**Required Variables**:

- `DATABASE_URL` (preferred) OR individual DB\_\* variables
- `DB_HOST`, `DB_USER`, `DB_PASS`, `DB_NAME`, `DB_PORT`
- `GROQ_API_KEY` or `XAI_API_KEY`

#### 4. **Network/Firewall Issues**

Render servers might be blocked by Supabase firewall.

**Solution**:

- In Supabase Dashboard → Settings → Database → Network Restrictions
- Add `0.0.0.0/0` to allow all IPs (or Render's IP ranges if available)

### Step-by-Step Fix

#### Step 1: Update Supabase Settings

1. Go to your Supabase project dashboard
2. Navigate to Settings → Database
3. Copy the **Connection Pooling** URL (not Direct Connection)
4. Ensure SSL is enabled

#### Step 2: Update Environment Variables in Render

1. Go to your Render service dashboard
2. Navigate to Environment tab
3. Update `DATABASE_URL` with the connection pooling URL:
   ```
   postgresql://postgres.[project-ref]:[password]@aws-0-[region].pooler.supabase.com:6543/postgres?sslmode=require
   ```

#### Step 3: Test Connection Locally

Run the test script to verify connection:

```bash
python test_db_connection.py
```

#### Step 4: Deploy with New Configuration

1. Commit your changes
2. Push to your repository
3. Render will automatically redeploy

### Alternative Solutions

#### Option 1: Use Render PostgreSQL

Instead of Supabase, use Render's built-in PostgreSQL:

1. Create a PostgreSQL service in Render
2. Update your environment variables to use the Render database
3. Run your setup script to populate the database

#### Option 2: Use Railway or Vercel

If Render continues to have issues:

- **Railway**: Better PostgreSQL integration
- **Vercel**: Good for serverless deployments

### Testing Your Deployment

#### Local Testing

```bash
# Test database connection
python test_db_connection.py

# Test the API locally
python start.py
```

#### Production Testing

After deployment, test these endpoints:

- `GET /api/health` - Basic health check
- `GET /api/status` - Database connection status
- `POST /api/search` - Search functionality

### Common Error Messages & Solutions

#### "Network is unreachable"

- Use connection pooling URL
- Check Supabase network restrictions
- Verify SSL configuration

#### "SSL connection required"

- Add `?sslmode=require` to DATABASE_URL
- Ensure Supabase SSL is enabled

#### "Connection timeout"

- Use connection pooling instead of direct connection
- Check if Render IPs are whitelisted in Supabase

#### "Authentication failed"

- Verify username/password in environment variables
- Check if user has proper permissions

### Environment Variable Template

For Render deployment, set these environment variables:

```env
# Database (use connection pooling URL)
DATABASE_URL=postgresql://postgres.[project-ref]:[password]@aws-0-[region].pooler.supabase.com:6543/postgres?sslmode=require

# Backup individual parameters
DB_HOST=aws-0-[region].pooler.supabase.com
DB_PORT=6543
DB_NAME=postgres
DB_USER=postgres
DB_PASS=[your-password]
DB_TABLE_NAME=documents

# API Keys
GROQ_API_KEY=[your-groq-key]
XAI_API_KEY=[your-xai-key]

# Flask Configuration
FLASK_HOST=0.0.0.0
FLASK_PORT=10000
FLASK_DEBUG=False
FLASK_ENV=production
```

### Support

If you continue to have issues:

1. Check Render logs for detailed error messages
2. Test connection locally with `test_db_connection.py`
3. Verify Supabase dashboard settings
4. Consider using Render PostgreSQL instead of Supabase
