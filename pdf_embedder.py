"""
PDF Embedding System using Nomic Embeddings and PostgreSQL
"""

import os
import fitz  # PyMuPDF
import psycopg2
from psycopg2.extras import execute_values
import requests
import json
from typing import List, Dict, Tuple
from dotenv import load_dotenv
import hashlib
from pathlib import Path

# Load environment variables
load_dotenv()

class NomicEmbedder:
    """Embedder using nomic-ai/nomic-embed-text-v1 via Ollama"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.model_name = "nomic-embed-text:latest"
        
    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a list of texts using Ollama"""
        embeddings = []
        
        for text in texts:
            try:
                response = requests.post(
                    f"{self.base_url}/api/embeddings",
                    json={
                        "model": self.model_name,
                        "prompt": text
                    }
                )
                
                if response.status_code == 200:
                    embedding = response.json()["embedding"]
                    embeddings.append(embedding)
                else:
                    print(f"Error getting embedding: {response.status_code}")
                    embeddings.append([0.0] * 768)  # Default dimension for nomic
                    
            except Exception as e:
                print(f"Error processing text: {e}")
                embeddings.append([0.0] * 768)
                
        return embeddings

class PDFProcessor:
    """Process PDFs and extract text with chunking strategy"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text from PDF file"""
        try:
            doc = fitz.open(pdf_path)
            text = ""
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text += page.get_text()
                
            doc.close()
            return text.strip()
            
        except Exception as e:
            print(f"Error extracting text from {pdf_path}: {e}")
            return ""
    
    def chunk_text(self, text: str, filename: str) -> List[Dict]:
        """Split text into chunks based on headings and logical structure"""
        if not text:
            return []

        chunks = []

        # First, try to split by markdown-style headings
        heading_chunks = self._split_by_headings(text, filename)
        if heading_chunks:
            return heading_chunks

        # If no clear headings, try to split by common medical document patterns
        pattern_chunks = self._split_by_medical_patterns(text, filename)
        if pattern_chunks:
            return pattern_chunks

        # Fallback to word-based chunking if no structure is found
        return self._split_by_words(text, filename)

    def _split_by_headings(self, text: str, filename: str) -> List[Dict]:
        """Split text based on markdown-style headings (# ## ###)"""
        chunks = []
        lines = text.split('\n')

        current_chunk = []
        current_heading = "Introduction"
        chunk_index = 0

        for line in lines:
            line = line.strip()

            # Check if line is a heading
            if line.startswith('#'):
                # Save previous chunk if it has content
                if current_chunk:
                    chunk_text = '\n'.join(current_chunk).strip()
                    if chunk_text:
                        chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_heading}".encode()).hexdigest()
                        chunks.append({
                            "chunk_id": chunk_id,
                            "filename": filename,
                            "chunk_index": chunk_index,
                            "text": chunk_text,
                            "heading": current_heading,
                            "word_count": len(chunk_text.split())
                        })
                        chunk_index += 1

                # Start new chunk
                current_heading = line.replace('#', '').strip()
                current_chunk = [line]
            else:
                current_chunk.append(line)

        # Add the last chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk).strip()
            if chunk_text:
                chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_heading}".encode()).hexdigest()
                chunks.append({
                    "chunk_id": chunk_id,
                    "filename": filename,
                    "chunk_index": chunk_index,
                    "text": chunk_text,
                    "heading": current_heading,
                    "word_count": len(chunk_text.split())
                })

        return chunks if chunks else []

    def _split_by_medical_patterns(self, text: str, filename: str) -> List[Dict]:
        """Split text based on common medical document patterns"""
        chunks = []

        # Common medical section patterns
        section_patterns = [
            r'(?i)^(symptoms?|signs?):?\s*$',
            r'(?i)^(treatment|therapy):?\s*$',
            r'(?i)^(diagnosis|diagnostic):?\s*$',
            r'(?i)^(occurrence|epidemiology):?\s*$',
            r'(?i)^(prevention|prophylaxis):?\s*$',
            r'(?i)^(etiology|cause|causes):?\s*$',
            r'(?i)^(pathogenesis|pathology):?\s*$',
            r'(?i)^(prognosis|outcome):?\s*$',
            r'(?i)^(overview|introduction):?\s*$'
        ]

        import re
        lines = text.split('\n')

        current_chunk = []
        current_section = "Overview"
        chunk_index = 0

        for line in lines:
            line_stripped = line.strip()

            # Check if line matches any medical section pattern
            is_section_header = False
            for pattern in section_patterns:
                if re.match(pattern, line_stripped):
                    is_section_header = True
                    break

            if is_section_header:
                # Save previous chunk
                if current_chunk:
                    chunk_text = '\n'.join(current_chunk).strip()
                    if chunk_text:
                        chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_section}".encode()).hexdigest()
                        chunks.append({
                            "chunk_id": chunk_id,
                            "filename": filename,
                            "chunk_index": chunk_index,
                            "text": chunk_text,
                            "heading": current_section,
                            "word_count": len(chunk_text.split())
                        })
                        chunk_index += 1

                # Start new section
                current_section = line_stripped.replace(':', '').strip()
                current_chunk = [line]
            else:
                current_chunk.append(line)

        # Add the last chunk
        if current_chunk:
            chunk_text = '\n'.join(current_chunk).strip()
            if chunk_text:
                chunk_id = hashlib.md5(f"{filename}_{chunk_index}_{current_section}".encode()).hexdigest()
                chunks.append({
                    "chunk_id": chunk_id,
                    "filename": filename,
                    "chunk_index": chunk_index,
                    "text": chunk_text,
                    "heading": current_section,
                    "word_count": len(chunk_text.split())
                })

        return chunks if chunks else []

    def _split_by_words(self, text: str, filename: str) -> List[Dict]:
        """Fallback word-based chunking"""
        chunks = []
        words = text.split()

        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk_text = " ".join(chunk_words)

            # Create chunk metadata
            chunk_id = hashlib.md5(f"{filename}_{i}_{chunk_text[:100]}".encode()).hexdigest()

            chunks.append({
                "chunk_id": chunk_id,
                "filename": filename,
                "chunk_index": len(chunks),
                "text": chunk_text,
                "heading": f"Section {len(chunks) + 1}",
                "word_count": len(chunk_words)
            })

        return chunks

class PostgreSQLManager:
    """Manage PostgreSQL database operations"""
    
    def __init__(self):
        self.db_config = {
            "dbname": os.getenv("DB_NAME", "kforce"),
            "user": os.getenv("DB_USER", "postgres"),
            "password": os.getenv("DB_PASS", "kforce"),
            "host": os.getenv("DB_HOST", "localhost"),
            "port": os.getenv("DB_PORT", "5444")
        }
        self.table_name = os.getenv("DB_TABLE_NAME", "documents")
        
    def get_connection(self):
        """Get database connection"""
        return psycopg2.connect(**self.db_config)
    
    def create_tables(self):
        """Create necessary tables for storing embeddings"""
        create_table_sql = f"""
        -- Enable pgvector extension
        CREATE EXTENSION IF NOT EXISTS vector;

        -- Create documents table
        CREATE TABLE IF NOT EXISTS {self.table_name} (
            id SERIAL PRIMARY KEY,
            chunk_id VARCHAR(255) UNIQUE NOT NULL,
            filename VARCHAR(255) NOT NULL,
            chunk_index INTEGER NOT NULL,
            content TEXT NOT NULL,
            heading VARCHAR(255),
            word_count INTEGER,
            embedding vector(768),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- Create index for vector similarity search
        CREATE INDEX IF NOT EXISTS {self.table_name}_embedding_idx
        ON {self.table_name} USING ivfflat (embedding vector_cosine_ops)
        WITH (lists = 100);

        -- Create index for filename searches
        CREATE INDEX IF NOT EXISTS {self.table_name}_filename_idx ON {self.table_name}(filename);
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(create_table_sql)
                    conn.commit()
                    print("Tables created successfully!")
                    
        except Exception as e:
            print(f"Error creating tables: {e}")
    
    def insert_documents(self, chunks: List[Dict], embeddings: List[List[float]]):
        """Insert document chunks and embeddings into database"""
        if len(chunks) != len(embeddings):
            raise ValueError("Number of chunks and embeddings must match")
            
        insert_sql = f"""
        INSERT INTO {self.table_name} (chunk_id, filename, chunk_index, content, heading, word_count, embedding)
        VALUES %s
        ON CONFLICT (chunk_id) DO UPDATE SET
            content = EXCLUDED.content,
            heading = EXCLUDED.heading,
            word_count = EXCLUDED.word_count,
            embedding = EXCLUDED.embedding,
            created_at = CURRENT_TIMESTAMP
        """

        # Prepare data for insertion
        data = []
        for chunk, embedding in zip(chunks, embeddings):
            data.append((
                chunk["chunk_id"],
                chunk["filename"],
                chunk["chunk_index"],
                chunk["text"],
                chunk.get("heading", "Unknown"),
                chunk["word_count"],
                embedding
            ))
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    execute_values(cur, insert_sql, data, template=None, page_size=100)
                    conn.commit()
                    print(f"Inserted {len(data)} document chunks")
                    
        except Exception as e:
            print(f"Error inserting documents: {e}")
    
    def get_document_count(self) -> int:
        """Get total number of documents in database"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(f"SELECT COUNT(*) FROM {self.table_name}")
                    return cur.fetchone()[0]
        except Exception as e:
            print(f"Error getting document count: {e}")
            return 0

class PDFEmbeddingPipeline:
    """Main pipeline for processing PDFs and creating embeddings"""
    
    def __init__(self):
        self.embedder = NomicEmbedder()
        self.processor = PDFProcessor()
        self.db_manager = PostgreSQLManager()
        
    def process_pdf_folder(self, folder_path: str):
        """Process all PDFs in a folder"""
        folder_path = Path(folder_path)
        
        if not folder_path.exists():
            print(f"Folder {folder_path} does not exist")
            return
            
        # Create database tables
        self.db_manager.create_tables()
        
        # Get all PDF files
        pdf_files = list(folder_path.glob("*.pdf"))
        print(f"Found {len(pdf_files)} PDF files")
        
        for pdf_file in pdf_files:
            print(f"\nProcessing: {pdf_file.name}")
            self.process_single_pdf(str(pdf_file))
            
        print(f"\nProcessing complete! Total documents in database: {self.db_manager.get_document_count()}")
    
    def process_single_pdf(self, pdf_path: str):
        """Process a single PDF file"""
        filename = Path(pdf_path).name
        
        # Extract text
        text = self.processor.extract_text_from_pdf(pdf_path)
        if not text:
            print(f"No text extracted from {filename}")
            return
            
        # Create chunks
        chunks = self.processor.chunk_text(text, filename)
        if not chunks:
            print(f"No chunks created from {filename}")
            return
            
        print(f"Created {len(chunks)} chunks")
        
        # Get embeddings
        texts = [chunk["text"] for chunk in chunks]
        embeddings = self.embedder.get_embeddings(texts)
        
        # Store in database
        self.db_manager.insert_documents(chunks, embeddings)
        print(f"Stored {len(chunks)} chunks for {filename}")

if __name__ == "__main__":
    # Initialize pipeline
    pipeline = PDFEmbeddingPipeline()
    
    # Process diseases folder
    diseases_folder = "diseases"
    pipeline.process_pdf_folder(diseases_folder)
